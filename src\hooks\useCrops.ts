'use client';

import { useState, useEffect, useCallback } from 'react';
import { Crop, CropState } from '../types/dashboard';

const CROPS_STORAGE_KEY = 'dashboard-crops';

const getDefaultCrops = (): Crop[] => [
  {
    id: 'wheat-field-1',
    name: 'Wheat Field',
    type: 'wheat',
    area: 22,
    imageUrl: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?auto=format&fit=crop&w=400&q=80',
    status: 'growing',
    plantingDate: new Date('2024-03-15'),
    expectedHarvestDate: new Date('2024-07-15'),
  },
  {
    id: 'corn-field-1',
    name: 'Corn Field',
    type: 'corn',
    area: 18,
    imageUrl: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?auto=format&fit=crop&w=400&q=80',
    status: 'growing',
    plantingDate: new Date('2024-04-01'),
    expectedHarvestDate: new Date('2024-08-15'),
  },
  {
    id: 'soybean-field-1',
    name: 'Soybean Field',
    type: 'soybean',
    area: 16,
    imageUrl: 'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?auto=format&fit=crop&w=400&q=80',
    status: 'growing',
    plantingDate: new Date('2024-05-01'),
    expectedHarvestDate: new Date('2024-09-15'),
  },
  {
    id: 'empty-field-1',
    name: 'Empty Field',
    type: 'other',
    area: 15,
    imageUrl: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=200&q=80',
    status: 'empty',
  },
];

const getDefaultCropState = (): CropState => ({
  crops: getDefaultCrops(),
  selectedCropId: 'wheat-field-1', // Default selected crop
});

export const useCrops = () => {
  const [cropState, setCropState] = useState<CropState>(getDefaultCropState());
  const [isLoading, setIsLoading] = useState(true);

  // Load crops from localStorage on mount
  useEffect(() => {
    try {
      const savedCrops = localStorage.getItem(CROPS_STORAGE_KEY);
      if (savedCrops) {
        const parsedState = JSON.parse(savedCrops);
        // Convert date strings back to Date objects
        const cropsWithDates = parsedState.crops.map((crop: any) => ({
          ...crop,
          plantingDate: crop.plantingDate ? new Date(crop.plantingDate) : undefined,
          expectedHarvestDate: crop.expectedHarvestDate ? new Date(crop.expectedHarvestDate) : undefined,
        }));
        setCropState({
          ...parsedState,
          crops: cropsWithDates,
        });
      } else {
        setCropState(getDefaultCropState());
      }
    } catch (error) {
      console.error('Failed to load crops:', error);
      setCropState(getDefaultCropState());
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save crops to localStorage whenever state changes
  const saveCropState = useCallback((newState: CropState) => {
    try {
      localStorage.setItem(CROPS_STORAGE_KEY, JSON.stringify(newState));
      setCropState(newState);
    } catch (error) {
      console.error('Failed to save crops:', error);
    }
  }, []);

  // Add a new crop
  const addCrop = useCallback((crop: Omit<Crop, 'id'>) => {
    const newCrop: Crop = {
      ...crop,
      id: `crop-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
    
    const newState = {
      ...cropState,
      crops: [...cropState.crops, newCrop],
      selectedCropId: newCrop.id, // Auto-select the new crop
    };
    
    saveCropState(newState);
  }, [cropState, saveCropState]);

  // Update an existing crop
  const updateCrop = useCallback((cropId: string, updates: Partial<Crop>) => {
    const newState = {
      ...cropState,
      crops: cropState.crops.map(crop =>
        crop.id === cropId ? { ...crop, ...updates } : crop
      ),
    };
    
    saveCropState(newState);
  }, [cropState, saveCropState]);

  // Delete a crop
  const deleteCrop = useCallback((cropId: string) => {
    const newCrops = cropState.crops.filter(crop => crop.id !== cropId);
    let newSelectedId = cropState.selectedCropId;
    
    // If we're deleting the selected crop, select another one
    if (cropState.selectedCropId === cropId) {
      newSelectedId = newCrops.length > 0 ? newCrops[0].id : null;
    }
    
    const newState = {
      crops: newCrops,
      selectedCropId: newSelectedId,
    };
    
    saveCropState(newState);
  }, [cropState, saveCropState]);

  // Select a crop
  const selectCrop = useCallback((cropId: string) => {
    const newState = {
      ...cropState,
      selectedCropId: cropId,
    };
    
    saveCropState(newState);
  }, [cropState, saveCropState]);

  // Get the currently selected crop
  const getSelectedCrop = useCallback(() => {
    if (!cropState.selectedCropId) return null;
    return cropState.crops.find(crop => crop.id === cropState.selectedCropId) || null;
  }, [cropState]);

  // Reset to default crops
  const resetCrops = useCallback(() => {
    const defaultState = getDefaultCropState();
    localStorage.removeItem(CROPS_STORAGE_KEY);
    saveCropState(defaultState);
  }, [saveCropState]);

  return {
    crops: cropState.crops,
    selectedCropId: cropState.selectedCropId,
    selectedCrop: getSelectedCrop(),
    isLoading,
    addCrop,
    updateCrop,
    deleteCrop,
    selectCrop,
    resetCrops,
  };
};
