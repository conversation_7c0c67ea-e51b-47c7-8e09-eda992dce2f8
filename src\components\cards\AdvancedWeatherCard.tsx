'use client';

import React from 'react';

interface WeatherData {
  location: string;
  currentTime: string;
  temperature: number;
  feelsLike: number;
  condition: string;
  description: string;
  humidity: number;
  precipitation: number;
  radiation: number;
  windDirection: string;
  windSpeed: number;
  pressure: number;
  co2: number;
  airQuality: {
    index: number;
    pm1: number;
    pm25: number;
    pm10: number;
    co: number;
    no: number;
    no2: number;
    o2: number;
  };
}

// Mock weather data - in a real app this would come from an API
const weatherData: WeatherData = {
  location: "Lincoln, England, United Kingdom",
  currentTime: "9:07 AM",
  temperature: 15,
  feelsLike: 17,
  condition: "mostly_sunny",
  description: "Expect partly sunny skies. The high will be 19°.",
  humidity: 64,
  precipitation: 0,
  radiation: 245,
  windDirection: "NW",
  windSpeed: 13,
  pressure: 1012,
  co2: 415,
  airQuality: {
    index: 26,
    pm1: 8.2,
    pm25: 12.5,
    pm10: 18.3,
    co: 0.4,
    no: 2.1,
    no2: 15.7,
    o2: 20.9
  }
};

const WeatherIcon: React.FC<{ condition: string; size?: number }> = ({ condition, size = 64 }) => {
  const getIcon = () => {
    switch (condition) {
      case 'sunny':
        return (
          <div className="relative">
            <div className="w-16 h-16 bg-yellow-400 rounded-full shadow-lg" />
            <div className="absolute inset-0 animate-pulse">
              <div className="w-16 h-16 bg-yellow-300 rounded-full opacity-60" />
            </div>
          </div>
        );
      case 'mostly_sunny':
        return (
          <div className="relative flex items-center">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full shadow-lg" />
            <div className="w-8 h-8 bg-white rounded-full ml-2 shadow-lg border-2 border-gray-200 opacity-80" />
          </div>
        );
      case 'cloudy':
        return <div className="w-16 h-12 bg-gray-300 rounded-full relative shadow-lg" />;
      case 'rainy':
        return (
          <div className="relative">
            <div className="w-16 h-12 bg-gray-500 rounded-full shadow-lg" />
            <div className="absolute bottom-0 left-2 w-1 h-3 bg-blue-400 rounded-full" />
            <div className="absolute bottom-0 left-4 w-1 h-4 bg-blue-400 rounded-full" />
            <div className="absolute bottom-0 left-6 w-1 h-3 bg-blue-400 rounded-full" />
          </div>
        );
      default:
        return <div className="w-16 h-12 bg-blue-400 rounded-full shadow-lg" />;
    }
  };

  return <div className="flex items-center justify-center">{getIcon()}</div>;
};

const getBackgroundGradient = (condition: string, temperature: number) => {
  const timeOfDay = new Date().getHours();
  const isNight = timeOfDay < 6 || timeOfDay > 20;
  
  if (isNight) {
    return 'bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900';
  }
  
  switch (condition) {
    case 'sunny':
      return temperature > 25 
        ? 'bg-gradient-to-br from-orange-400 via-yellow-400 to-amber-300'
        : 'bg-gradient-to-br from-blue-400 via-sky-400 to-cyan-300';
    case 'mostly_sunny':
      return 'bg-gradient-to-br from-blue-500 via-sky-400 to-blue-300';
    case 'cloudy':
      return 'bg-gradient-to-br from-gray-500 via-gray-400 to-slate-400';
    case 'rainy':
      return 'bg-gradient-to-br from-gray-600 via-slate-500 to-gray-500';
    default:
      return 'bg-gradient-to-br from-blue-500 via-sky-400 to-blue-300';
  }
};

export const AdvancedWeatherCard: React.FC = () => {
  const backgroundClass = getBackgroundGradient(weatherData.condition, weatherData.temperature);

  return (
    <div className={`${backgroundClass} rounded-2xl shadow-lg p-4 text-white h-full relative overflow-hidden`}>
      {/* Background overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-20 rounded-2xl" />

      <div className="relative z-10 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
              <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" strokeWidth="2" fill="none"/>
              <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2" fill="none"/>
            </svg>
            <span className="text-xs font-medium opacity-90">{weatherData.location}</span>
            <span className="text-xs opacity-75">• {weatherData.currentTime}</span>
          </div>
          <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded px-2 py-1 text-xs transition-all backdrop-blur-sm">
            💬 Seeing different weather?
          </button>
        </div>

        {/* Main weather display */}
        <div className="flex items-center gap-3 mb-2">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full shadow-lg mr-2" />
            <div className="w-6 h-6 bg-white rounded-full shadow-lg border border-gray-200 opacity-80" />
          </div>
          <div className="flex-1">
            <div className="text-2xl font-bold">{weatherData.temperature}°C</div>
            <div className="text-sm opacity-90 capitalize">{weatherData.condition.replace('_', ' ')}</div>
            <div className="text-xs opacity-75">Feels like {weatherData.feelsLike}°</div>
          </div>
          <div className="text-xs opacity-90 bg-white bg-opacity-10 rounded px-2 py-1 backdrop-blur-sm max-w-xs">
            {weatherData.description}
          </div>
        </div>

        {/* Weather Metrics */}
        <div className="mb-3">
          <div className="grid grid-cols-8 gap-1.5 text-xs mb-2">
            {/* Meteorological metrics */}
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Temperature</div>
              <div className="font-semibold">{weatherData.temperature}°C</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Humidity</div>
              <div className="font-semibold">{weatherData.humidity}%</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Radiation</div>
              <div className="font-semibold">{weatherData.radiation} W/m²</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Precipitation</div>
              <div className="font-semibold">{weatherData.precipitation}mm</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Wind Speed</div>
              <div className="font-semibold">{weatherData.windSpeed} km/h</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Wind Dir.</div>
              <div className="font-semibold">{weatherData.windDirection}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Pressure</div>
              <div className="font-semibold">{weatherData.pressure} mb</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">Dew Point</div>
              <div className="font-semibold">9°C</div>
            </div>
          </div>
        </div>

        {/* Air Quality Section */}
        <div className="border-t border-white border-opacity-20 pt-2">
          <div className="text-xs opacity-75 mb-2 font-medium">Air Quality & Environmental</div>
          <div className="grid grid-cols-9 gap-1.5 text-xs">
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">AQI</div>
              <div className="font-semibold">{weatherData.airQuality.index}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">PM1</div>
              <div className="font-semibold">{weatherData.airQuality.pm1}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">PM2.5</div>
              <div className="font-semibold">{weatherData.airQuality.pm25}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">PM10</div>
              <div className="font-semibold">{weatherData.airQuality.pm10}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">CO</div>
              <div className="font-semibold">{weatherData.airQuality.co}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">NO</div>
              <div className="font-semibold">{weatherData.airQuality.no}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">NO₂</div>
              <div className="font-semibold">{weatherData.airQuality.no2}</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">O₂</div>
              <div className="font-semibold">{weatherData.airQuality.o2}%</div>
            </div>
            <div className="text-center bg-white bg-opacity-10 rounded p-1.5 backdrop-blur-sm">
              <div className="opacity-75 text-xs">CO₂</div>
              <div className="font-semibold">{weatherData.co2}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
