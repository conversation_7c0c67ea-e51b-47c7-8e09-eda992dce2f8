'use client';

import React, { useState } from 'react';
import { Crop } from '../types/dashboard';

interface AddCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddCrop: (crop: Omit<Crop, 'id'>) => void;
}

const cropTypeOptions = [
  { value: 'wheat', label: 'Wheat', image: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?auto=format&fit=crop&w=400&q=80' },
  { value: 'corn', label: 'Corn', image: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?auto=format&fit=crop&w=400&q=80' },
  { value: 'soybean', label: 'Soybean', image: 'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?auto=format&fit=crop&w=400&q=80' },
  { value: 'rice', label: 'Rice', image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?auto=format&fit=crop&w=400&q=80' },
  { value: 'cotton', label: 'Cotton', image: 'https://images.unsplash.com/photo-1605000797499-95a51c5269ae?auto=format&fit=crop&w=400&q=80' },
  { value: 'other', label: 'Other/Mixed', image: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80' },
] as const;

const statusOptions = [
  { value: 'empty', label: 'Empty Field' },
  { value: 'planted', label: 'Recently Planted' },
  { value: 'growing', label: 'Growing' },
  { value: 'ready', label: 'Ready for Harvest' },
  { value: 'harvested', label: 'Harvested' },
] as const;

export const AddCropModal: React.FC<AddCropModalProps> = ({
  isOpen,
  onClose,
  onAddCrop,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'wheat' as Crop['type'],
    area: '',
    status: 'empty' as Crop['status'],
    plantingDate: '',
    expectedHarvestDate: '',
    notes: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Crop name is required';
    }

    if (!formData.area || parseFloat(formData.area) <= 0) {
      newErrors.area = 'Area must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const selectedCropType = cropTypeOptions.find(option => option.value === formData.type);
    
    const newCrop: Omit<Crop, 'id'> = {
      name: formData.name.trim(),
      type: formData.type,
      area: parseFloat(formData.area),
      imageUrl: selectedCropType?.image || cropTypeOptions[0].image,
      status: formData.status,
      plantingDate: formData.plantingDate ? new Date(formData.plantingDate) : undefined,
      expectedHarvestDate: formData.expectedHarvestDate ? new Date(formData.expectedHarvestDate) : undefined,
      notes: formData.notes.trim() || undefined,
    };

    onAddCrop(newCrop);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      name: '',
      type: 'wheat',
      area: '',
      status: 'empty',
      plantingDate: '',
      expectedHarvestDate: '',
      notes: '',
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-800">Add New Crop</h2>
            <button
              onClick={handleClose}
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Crop Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Crop Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., North Field Wheat"
              />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
            </div>

            {/* Crop Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Crop Type
              </label>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {cropTypeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Area */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Area (hectares) *
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                value={formData.area}
                onChange={(e) => handleInputChange('area', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.area ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., 25.5"
              />
              {errors.area && <p className="text-red-500 text-xs mt-1">{errors.area}</p>}
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Current Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Planting Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Planting Date (optional)
              </label>
              <input
                type="date"
                value={formData.plantingDate}
                onChange={(e) => handleInputChange('plantingDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Expected Harvest Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expected Harvest Date (optional)
              </label>
              <input
                type="date"
                value={formData.expectedHarvestDate}
                onChange={(e) => handleInputChange('expectedHarvestDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes (optional)
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                placeholder="Additional notes about this crop..."
              />
            </div>

            {/* Buttons */}
            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add Crop
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
